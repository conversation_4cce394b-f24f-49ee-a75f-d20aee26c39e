import { useState } from "react";
import { Form } from "@remix-run/react";
import { Button } from "~/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>eader, CardTitle } from "~/components/ui/card";
import { DynamicField } from "~/components/dynamic/DynamicField";
import type { CustomFieldDefinition } from "~/models/metadata.server";

interface DynamicFormProps {
  entityType: string;
  entityId?: string;
  fields: CustomFieldDefinition[];
  children?: React.ReactNode;
  defaultValues?: Record<string, any>;
  onSubmit?: (data: FormData) => void;
  submitButtonText?: string;
  title?: string;
  description?: string;
  isSubmitting?: boolean;
  errors?: Record<string, string>;
  className?: string;
}

export function DynamicForm({
  entityType,
  entityId,
  fields,
  defaultValues = {},
  onSubmit,
  submitButtonText = "Save",
  title,
  description,
  isSubmitting = false,
  errors = {},
  className = ""
}: DynamicFormProps) {
  // State to track field values
  const [values, setValues] = useState<Record<string, any>>(defaultValues);

  // Handle field value changes
  const handleFieldChange = (fieldName: string, value: any) => {
    setValues(prev => ({
      ...prev,
      [fieldName]: value
    }));
  };

  // Sort fields by order
  const sortedFields = [...fields].sort((a, b) => a.order - b.order);

  return (
    <Card className={className}>
      {title && (
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          {description && <p className="text-sm text-muted-foreground">{description}</p>}
        </CardHeader>
      )}

      <CardContent>
        <Form method="post" onSubmit={onSubmit} className="space-y-6">
          <input type="hidden" name="entityType" value={entityType} />
          {entityId && <input type="hidden" name="entityId" value={entityId} />}
          <input type="hidden" name="customFieldsData" value={JSON.stringify(values)} />

          <div className="space-y-4">
            {sortedFields.map(field => (
              <DynamicField
                key={field.id}
                field={field}
                value={values[field.name]}
                onChange={(value) => handleFieldChange(field.name, value)}
                error={errors[field.name]}
                disabled={isSubmitting}
              />
            ))}
          </div>

          <CardFooter className="px-0 pt-4">
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Saving..." : submitButtonText}
            </Button>
          </CardFooter>
        </Form>
      </CardContent>
    </Card>
  );
}
import { Input } from "~/components/ui/input";
import { Textarea } from "~/components/ui/textarea";
import { Checkbox } from "~/components/ui/checkbox";
import { Label } from "~/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { DatePicker } from "~/components/ui/date-picker";
import type { CustomFieldDefinition } from "~/models/metadata.server";

interface DynamicFieldProps {
  field: CustomFieldDefinition;
  value: any;
  onChange: (value: any) => void;
  error?: string;
  disabled?: boolean;
}

export function DynamicField({
  field,
  value,
  onChange,
  error,
  disabled = false
}: DynamicFieldProps) {
  const fieldId = `field-${field.name}`;

  // Parse options for select fields
  const options = field.options ? JSON.parse(field.options) : [];

  // Render the appropriate field based on type
  const renderField = () => {
    switch (field.fieldType) {
      case 'TEXT':
        return (
          <Input
            id={fieldId}
            name={field.name}
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={field.placeholder || ''}
            disabled={disabled}
            aria-invalid={error ? true : undefined}
            aria-errormessage={error ? `${fieldId}-error` : undefined}
          />
        );

      case 'NUMBER':
        return (
          <Input
            id={fieldId}
            name={field.name}
            type="number"
            value={value || ''}
            onChange={(e) => onChange(parseFloat(e.target.value))}
            placeholder={field.placeholder || ''}
            disabled={disabled}
            aria-invalid={error ? true : undefined}
            aria-errormessage={error ? `${fieldId}-error` : undefined}
          />
        );

      case 'RICH_TEXT':
        return (
          <Textarea
            id={fieldId}
            name={field.name}
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={field.placeholder || ''}
            rows={5}
            disabled={disabled}
            aria-invalid={error ? true : undefined}
            aria-errormessage={error ? `${fieldId}-error` : undefined}
          />
        );

      case 'BOOLEAN':
        return (
          <div className="flex items-center space-x-2">
            <Checkbox
              id={fieldId}
              name={field.name}
              checked={!!value}
              onCheckedChange={onChange}
              disabled={disabled}
            />
          </div>
        );

      case 'SELECT':
        return (
          <Select
            name={field.name}
            value={value || ''}
            onValueChange={onChange}
            disabled={disabled}
          >
            <SelectTrigger id={fieldId}>
              <SelectValue placeholder={field.placeholder || 'Select an option'} />
            </SelectTrigger>
            <SelectContent>
              {options.map((option: string) => (
                <SelectItem key={option} value={option}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case 'DATE':
        return (
          <DatePicker
            id={fieldId}
            name={field.name}
            value={value ? new Date(value) : undefined}
            onChange={onChange}
            disabled={disabled}
          />
        );

      case 'EMAIL':
        return (
          <Input
            id={fieldId}
            name={field.name}
            type="email"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={field.placeholder || '<EMAIL>'}
            disabled={disabled}
            aria-invalid={error ? true : undefined}
            aria-errormessage={error ? `${fieldId}-error` : undefined}
          />
        );

      case 'PHONE':
        return (
          <Input
            id={fieldId}
            name={field.name}
            type="tel"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={field.placeholder || '+****************'}
            disabled={disabled}
            aria-invalid={error ? true : undefined}
            aria-errormessage={error ? `${fieldId}-error` : undefined}
          />
        );

      case 'URL':
        return (
          <Input
            id={fieldId}
            name={field.name}
            type="url"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={field.placeholder || 'https://example.com'}
            disabled={disabled}
            aria-invalid={error ? true : undefined}
            aria-errormessage={error ? `${fieldId}-error` : undefined}
          />
        );

      default:
        return (
          <Input
            id={fieldId}
            name={field.name}
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={field.placeholder || ''}
            disabled={disabled}
            aria-invalid={error ? true : undefined}
            aria-errormessage={error ? `${fieldId}-error` : undefined}
          />
        );
    }
  };

  return (
    <div className="space-y-2">
      {field.fieldType !== 'BOOLEAN' && (
        <Label htmlFor={fieldId}>
          {field.label}
          {field.required && <span className="text-destructive ml-1">*</span>}
        </Label>
      )}

      {renderField()}

      {field.helpText && (
        <p className="text-sm text-muted-foreground">{field.helpText}</p>
      )}

      {error && (
        <p className="text-sm text-destructive" id={`${fieldId}-error`}>
          {error}
        </p>
      )}
    </div>
  );
}
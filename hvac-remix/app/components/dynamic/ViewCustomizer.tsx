import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Label } from "~/components/ui/label";
import { GripVertical, Eye, EyeOff } from "lucide-react";

interface ViewCustomizerProps {
  availableFields: Array<{ id: string; name: string; label: string }>;
  viewDefinitions: Array<{ id: string; name: string; layout: any }>;
  currentView?: { id: string; name: string; layout: any };
  onViewChange: (viewId: string) => void;
  onSaveView: (layout: any) => void;
  className?: string;
}

// Simple field component without drag-and-drop
function SimpleField({ field, isVisible, onToggleVisibility }: {
  field: { id: string; name: string; label: string };
  isVisible: boolean;
  onToggleVisibility: (fieldId: string) => void;
}) {
  return (
    <div className="flex items-center justify-between p-3 bg-white border rounded-md mb-2">
      <div className="flex items-center">
        <GripVertical className="h-4 w-4 mr-2 text-gray-400" />
        <span>{field.label}</span>
      </div>

      <Button
        type="button"
        variant="ghost"
        size="sm"
        onClick={() => onToggleVisibility(field.id)}
      >
        {isVisible ? (
          <Eye className="h-4 w-4" />
        ) : (
          <EyeOff className="h-4 w-4 text-gray-400" />
        )}
      </Button>
    </div>
  );
}

export function ViewCustomizer({
  availableFields,
  viewDefinitions,
  currentView,
  onViewChange,
  onSaveView,
  className = ""
}: ViewCustomizerProps) {
  // Parse current view layout
  const initialLayout = currentView?.layout ? JSON.parse(currentView.layout) : {
    visibleFields: availableFields.map(f => f.id),
    order: availableFields.map(f => f.id)
  };

  const [layout, setLayout] = useState(initialLayout);
  const [isCustomizing, setIsCustomizing] = useState(false);

  // Handle field reordering (simplified without drag-and-drop)
  const moveFieldUp = (fieldId: string) => {
    setLayout(prev => {
      const currentIndex = prev.order.indexOf(fieldId);
      if (currentIndex > 0) {
        const newOrder = [...prev.order];
        [newOrder[currentIndex - 1], newOrder[currentIndex]] = [newOrder[currentIndex], newOrder[currentIndex - 1]];
        return { ...prev, order: newOrder };
      }
      return prev;
    });
  };

  const moveFieldDown = (fieldId: string) => {
    setLayout(prev => {
      const currentIndex = prev.order.indexOf(fieldId);
      if (currentIndex < prev.order.length - 1) {
        const newOrder = [...prev.order];
        [newOrder[currentIndex], newOrder[currentIndex + 1]] = [newOrder[currentIndex + 1], newOrder[currentIndex]];
        return { ...prev, order: newOrder };
      }
      return prev;
    });
  };

  // Toggle field visibility
  const toggleFieldVisibility = (fieldId: string) => {
    setLayout(prev => {
      const visibleFields = [...prev.visibleFields];

      if (visibleFields.includes(fieldId)) {
        return {
          ...prev,
          visibleFields: visibleFields.filter(id => id !== fieldId)
        };
      } else {
        return {
          ...prev,
          visibleFields: [...visibleFields, fieldId]
        };
      }
    });
  };

  // Save the current layout
  const handleSave = () => {
    onSaveView(layout);
    setIsCustomizing(false);
  };

  // Get fields in the correct order
  const orderedFields = layout.order
    .map(id => availableFields.find(f => f.id === id))
    .filter(Boolean);

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>View Customization</CardTitle>
        </div>

        <div className="flex items-center space-x-2">
          <Select
            value={currentView?.id || ""}
            onValueChange={onViewChange}
            disabled={isCustomizing}
          >
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Select a view" />
            </SelectTrigger>
            <SelectContent>
              {viewDefinitions.map(view => (
                <SelectItem key={view.id} value={view.id}>
                  {view.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {isCustomizing ? (
            <div className="flex space-x-2">
              <Button variant="outline" onClick={() => setIsCustomizing(false)}>
                Cancel
              </Button>
              <Button onClick={handleSave}>
                Save
              </Button>
            </div>
          ) : (
            <Button onClick={() => setIsCustomizing(true)}>
              Customize
            </Button>
          )}
        </div>
      </CardHeader>

      {isCustomizing && (
        <CardContent>
          <div className="mb-4">
            <Label>Reorder fields or toggle visibility</Label>
          </div>

          <div className="space-y-2">
            {orderedFields.map(field => (
              <SimpleField
                key={field.id}
                field={field}
                isVisible={layout.visibleFields.includes(field.id)}
                onToggleVisibility={toggleFieldVisibility}
              />
            ))}
          </div>
        </CardContent>
      )}
    </Card>
  );
}
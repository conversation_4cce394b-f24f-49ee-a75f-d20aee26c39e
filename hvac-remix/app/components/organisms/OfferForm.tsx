import { useState } from "react";
import { Form, useNavigation, useActionData } from "@remix-run/react";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "~/components/ui/card";
import { CustomerSelect } from "~/components/molecules/CustomerSelect";
import { DatePicker } from "~/components/ui/date-picker";
import type { Customer, OfferTemplate } from "@prisma/client";

interface OfferFormProps {
  defaultValues?: {
    id?: string;
    title?: string;
    description?: string;
    status?: string;
    validUntil?: Date | null;
    totalAmount?: number;
    taxAmount?: number | null;
    discountAmount?: number | null;
    notes?: string | null;
    customerId?: string;
    templateId?: string | null;
    opportunityId?: string;
  };
  customers: Pick<Customer, "id" | "name">[];
  templates: Pick<OfferTemplate, "id" | "name">[];
  mode?: "create" | "edit";
}

export function OfferForm({
  defaultValues = {},
  customers,
  templates,
  mode = "create",
}: OfferFormProps) {
  const navigation = useNavigation();
  const actionData = useActionData<{ errors?: Record<string, string> }>();
  const isSubmitting = navigation.state === "submitting";

  const [selectedCustomerId, setSelectedCustomerId] = useState(defaultValues.customerId || "");
  const [selectedTemplateId, setSelectedTemplateId] = useState(defaultValues.templateId || "");
  const [validUntil, setValidUntil] = useState<Date | undefined>(
    defaultValues.validUntil ? new Date(defaultValues.validUntil) : undefined
  );

  return (
    <Form method="post" className="space-y-6">
      {defaultValues.id && <input type="hidden" name="id" value={defaultValues.id} />}
      {defaultValues.opportunityId && <input type="hidden" name="opportunityId" value={defaultValues.opportunityId} />}

      <div className="space-y-4">
        <div>
          <Label htmlFor="title">Title</Label>
          <Input
            id="title"
            name="title"
            defaultValue={defaultValues.title}
            required
            aria-invalid={actionData?.errors?.title ? true : undefined}
            aria-errormessage={actionData?.errors?.title ? "title-error" : undefined}
          />
          {actionData?.errors?.title && (
            <div className="text-destructive text-sm pt-1" id="title-error">
              {actionData.errors.title}
            </div>
          )}
        </div>

        <div>
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            name="description"
            defaultValue={defaultValues.description || ""}
            rows={3}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="customer">Customer</Label>
            <CustomerSelect
              id="customer"
              name="customerId"
              customers={customers}
              defaultValue={selectedCustomerId}
              onChange={setSelectedCustomerId}
              required
            />
            {actionData?.errors?.customerId && (
              <div className="text-destructive text-sm pt-1">
                {actionData.errors.customerId}
              </div>
            )}
          </div>

          <div>
            <Label htmlFor="template">Template</Label>
            <Select
              name="templateId"
              value={selectedTemplateId}
              onValueChange={setSelectedTemplateId}
            >
              <SelectTrigger id="template">
                <SelectValue placeholder="Select a template" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">None</SelectItem>
                {templates.map((template) => (
                  <SelectItem key={template.id} value={template.id}>
                    {template.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label htmlFor="totalAmount">Total Amount</Label>
            <Input
              id="totalAmount"
              name="totalAmount"
              type="number"
              step="0.01"
              min="0"
              defaultValue={defaultValues.totalAmount?.toString() || "0"}
              required
            />
            {actionData?.errors?.totalAmount && (
              <div className="text-destructive text-sm pt-1">
                {actionData.errors.totalAmount}
              </div>
            )}
          </div>

          <div>
            <Label htmlFor="taxAmount">Tax Amount</Label>
            <Input
              id="taxAmount"
              name="taxAmount"
              type="number"
              step="0.01"
              min="0"
              defaultValue={defaultValues.taxAmount?.toString() || ""}
            />
          </div>

          <div>
            <Label htmlFor="discountAmount">Discount Amount</Label>
            <Input
              id="discountAmount"
              name="discountAmount"
              type="number"
              step="0.01"
              min="0"
              defaultValue={defaultValues.discountAmount?.toString() || ""}
            />
          </div>
        </div>

        <div>
          <Label htmlFor="validUntil">Valid Until</Label>
          <DatePicker
            id="validUntil"
            name="validUntil"
            date={validUntil}
            setDate={setValidUntil}
          />
        </div>

        {mode === "edit" && (
          <div>
            <Label htmlFor="status">Status</Label>
            <Select name="status" defaultValue={defaultValues.status || "DRAFT"}>
              <SelectTrigger id="status">
                <SelectValue placeholder="Select a status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="DRAFT">Draft</SelectItem>
                <SelectItem value="SENT">Sent</SelectItem>
                <SelectItem value="ACCEPTED">Accepted</SelectItem>
                <SelectItem value="REJECTED">Rejected</SelectItem>
                <SelectItem value="EXPIRED">Expired</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}

        <div>
          <Label htmlFor="notes">Notes</Label>
          <Textarea
            id="notes"
            name="notes"
            defaultValue={defaultValues.notes || ""}
            rows={3}
          />
        </div>
      </div>

      <div className="flex justify-end gap-2">
        <Button variant="outline" type="reset" disabled={isSubmitting}>
          Reset
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting
            ? mode === "create"
              ? "Creating..."
              : "Updating..."
            : mode === "create"
            ? "Create Offer"
            : "Update Offer"}
        </Button>
      </div>
    </Form>
  );
}
import type { Prisma } from "@prisma/client";
import { prisma } from "~/db.server";

// Define types for inventory parts
export type InventoryPartWithRelations = Prisma.InventoryPartGetPayload<{
  include: {
    supplier: true;
    inventoryTransactions: true;
    inventoryLocations: {
      include: {
        location: true;
      };
    };
  };
}>;

// Part batch operations
export type BatchOperation = {
  partId: string;
  quantity: number;
  locationId?: string;
  reason: string;
  referenceId?: string;
  referenceType?: string;
};

// Get inventory part by ID with related data
export async function getInventoryPart(id: string) {
  return prisma.inventoryPart.findUnique({
    where: { id },
    include: {
      supplier: true,
      inventoryTransactions: {
        orderBy: {
          transactionDate: "desc"
        },
        take: 50
      },
      inventoryLocations: {
        include: {
          location: true
        }
      }
    }
  });
}

// Get inventory analytics
export async function getInventoryAnalytics() {
  const [totalValue, lowStockCount, categoryCounts, locationBreakdown] = await Promise.all([
    // Calculate total inventory value
    prisma.inventoryPart.aggregate({
      _sum: {
        currentValuePLN: true
      }
    }),
    
    // Count low stock items
    prisma.inventoryPart.count({
      where: {
        currentStock: {
          lte: { reorderPoint: true }
        }
      }
    }),
    
    // Get category breakdown
    prisma.inventoryPart.groupBy({
      by: ["category"],
      _count: { id: true },
      _sum: { currentValuePLN: true }
    }),
    
    // Get location breakdown
    prisma.inventoryLocation.groupBy({
      by: ["locationId"],
      _sum: { quantity: true }
    })
  ]);
  
  // Get location names for the breakdown
  const locationIds = locationBreakdown.map(l => l.locationId);
  const locations = await prisma.location.findMany({
    where: {
      id: { in: locationIds }
    }
  });
  
  const locationsWithNames = locationBreakdown.map(lb => ({
    locationId: lb.locationId,
    locationName: locations.find(l => l.id === lb.locationId)?.name || "Unknown",
    quantity: lb._sum.quantity || 0
  }));
  
  return {
    totalValue: totalValue._sum.currentValuePLN || 0,
    lowStockCount,
    categoryCounts,
    locationBreakdown: locationsWithNames
  };
}

// Get parts that need reordering
export async function getReorderList() {
  return prisma.inventoryPart.findMany({
    where: {
      isActive: true,
      currentStock: {
        lte: { reorderPoint: true }
      }
    },
    include: {
      supplier: true
    },
    orderBy: [
      {
        category: "asc"
      },
      {
        currentStock: "asc"
      }
    ]
  });
}

// Get parts usage history
export async function getPartsUsageHistory(days: number = 90) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  const transactions = await prisma.inventoryTransaction.findMany({
    where: {
      transactionDate: {
        gte: startDate
      },
      transactionType: "USAGE"
    },
    include: {
      part: true
    },
    orderBy: {
      transactionDate: "desc"
    }
  });
  
  // Aggregate by part
  const usageByPart = transactions.reduce((acc, transaction) => {
    const partId = transaction.partId;
    if (!acc[partId]) {
      acc[partId] = {
        part: transaction.part,
        totalUsage: 0,
        transactions: []
      };
    }
    
    acc[partId].totalUsage += transaction.quantity;
    acc[partId].transactions.push(transaction);
    
    return acc;
  }, {} as Record<string, { part: any, totalUsage: number, transactions: any[] }>);
  
  return Object.values(usageByPart).sort((a, b) => b.totalUsage - a.totalUsage);
}

// Add stock for a part
export async function addPartStock(partId: string, quantity: number, locationId: string, reason: string, userId: string, referenceId?: string, referenceType?: string) {
  return prisma.$transaction(async (tx) => {
    // Update the part stock
    const part = await tx.inventoryPart.update({
      where: { id: partId },
      data: {
        currentStock: {
          increment: quantity
        },
        // Recalculate the current value
        currentValuePLN: {
          set: prisma.inventoryPart.findUnique({
            where: { id: partId }
          }).then(p => (p!.currentStock + quantity) * (p!.costPrice || 0))
        }
      }
    });
    
    // Add or update inventory location quantity
    await tx.inventoryLocation.upsert({
      where: {
        partId_locationId: {
          partId,
          locationId
        }
      },
      create: {
        partId,
        locationId,
        quantity
      },
      update: {
        quantity: {
          increment: quantity
        }
      }
    });
    
    // Record transaction
    await tx.inventoryTransaction.create({
      data: {
        partId,
        quantity,
        transactionType: "RECEIPT",
        locationId,
        reason,
        userId,
        referenceId,
        referenceType
      }
    });
    
    return part;
  });
}

// Remove stock from a part (usage or adjustment)
export async function removePartStock(partId: string, quantity: number, locationId: string, reason: string, userId: string, transactionType: "USAGE" | "ADJUSTMENT" = "USAGE", referenceId?: string, referenceType?: string) {
  return prisma.$transaction(async (tx) => {
    // Get the current location quantity
    const locationInventory = await tx.inventoryLocation.findUnique({
      where: {
        partId_locationId: {
          partId,
          locationId
        }
      }
    });
    
    if (!locationInventory || locationInventory.quantity < quantity) {
      throw new Error(`Not enough stock in this location. Available: ${locationInventory?.quantity || 0}`);
    }
    
    // Update the part stock
    const part = await tx.inventoryPart.update({
      where: { id: partId },
      data: {
        currentStock: {
          decrement: quantity
        },
        // Recalculate the current value
        currentValuePLN: {
          set: prisma.inventoryPart.findUnique({
            where: { id: partId }
          }).then(p => (p!.currentStock - quantity) * (p!.costPrice || 0))
        }
      }
    });
    
    // Update inventory location quantity
    await tx.inventoryLocation.update({
      where: {
        partId_locationId: {
          partId,
          locationId
        }
      },
      data: {
        quantity: {
          decrement: quantity
        }
      }
    });
    
    // Record transaction
    await tx.inventoryTransaction.create({
      data: {
        partId,
        quantity: -quantity, // Negative for removal
        transactionType,
        locationId,
        reason,
        userId,
        referenceId,
        referenceType
      }
    });
    
    return part;
  });
}

// Transfer stock between locations
export async function transferStock(partId: string, quantity: number, fromLocationId: string, toLocationId: string, reason: string, userId: string) {
  return prisma.$transaction(async (tx) => {
    // Check if source has enough stock
    const sourceLocation = await tx.inventoryLocation.findUnique({
      where: {
        partId_locationId: {
          partId,
          locationId: fromLocationId
        }
      }
    });
    
    if (!sourceLocation || sourceLocation.quantity < quantity) {
      throw new Error(`Not enough stock in source location. Available: ${sourceLocation?.quantity || 0}`);
    }
    
    // Remove from source location
    await tx.inventoryLocation.update({
      where: {
        partId_locationId: {
          partId,
          locationId: fromLocationId
        }
      },
      data: {
        quantity: {
          decrement: quantity
        }
      }
    });
    
    // Add to destination location
    await tx.inventoryLocation.upsert({
      where: {
        partId_locationId: {
          partId,
          locationId: toLocationId
        }
      },
      create: {
        partId,
        locationId: toLocationId,
        quantity
      },
      update: {
        quantity: {
          increment: quantity
        }
      }
    });
    
    // Record transfer transaction
    await tx.inventoryTransaction.create({
      data: {
        partId,
        quantity: 0, // Net quantity change is 0 for transfer
        transactionType: "TRANSFER",
        locationId: fromLocationId, // Source location
        transferToLocationId: toLocationId, // Destination location
        reason,
        userId
      }
    });
    
    return tx.inventoryPart.findUnique({
      where: { id: partId },
      include: {
        inventoryLocations: {
          include: {
            location: true
          }
        }
      }
    });
  });
}

// Process batch operations (add/remove multiple parts at once)
export async function processBatchOperations(operations: BatchOperation[], transactionType: "RECEIPT" | "USAGE" | "ADJUSTMENT", userId: string, reason?: string) {
  return prisma.$transaction(async (tx) => {
    const results = [];
    
    for (const op of operations) {
      if (op.quantity === 0) continue;
      
      const locationId = op.locationId;
      if (!locationId) {
        throw new Error(`Location ID is required for part ${op.partId}`);
      }
      
      // Add or remove stock based on transaction type
      if (transactionType === "RECEIPT") {
        const result = await addPartStock(op.partId, op.quantity, locationId, op.reason || reason || "Batch receipt", userId, op.referenceId, op.referenceType);
        results.push(result);
      } else {
        const result = await removePartStock(op.partId, op.quantity, locationId, op.reason || reason || `Batch ${transactionType.toLowerCase()}`, userId, transactionType as any, op.referenceId, op.referenceType);
        results.push(result);
      }
    }
    
    return results;
  });
}

// Get inventory part usage history by service order
export async function getPartUsageByServiceOrder(serviceOrderId: string) {
  return prisma.inventoryTransaction.findMany({
    where: {
      referenceId: serviceOrderId,
      referenceType: "SERVICE_ORDER",
      transactionType: "USAGE"
    },
    include: {
      part: true,
      location: true,
      user: {
        select: {
          id: true,
          email: true,
          name: true
        }
      }
    },
    orderBy: {
      transactionDate: "desc"
    }
  });
}

// Get inventory alerts (low stock, expired, etc)
export async function getInventoryAlerts() {
  const [lowStockParts, expiringSoonParts] = await Promise.all([
    // Low stock parts
    prisma.inventoryPart.findMany({
      where: {
        isActive: true,
        currentStock: {
          lte: { minimumStock: true }
        }
      },
      include: {
        supplier: true
      },
      orderBy: [
        {
          minimumStock: "desc"
        },
        {
          currentStock: "asc"
        }
      ]
    }),
    
    // Parts expiring soon (within next 30 days)
    prisma.inventoryPart.findMany({
      where: {
        isActive: true,
        expirationDate: {
          gte: new Date(),
          lte: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
        }
      },
      orderBy: {
        expirationDate: "asc"
      }
    })
  ]);
  
  return {
    lowStockParts,
    expiringSoonParts
  };
}